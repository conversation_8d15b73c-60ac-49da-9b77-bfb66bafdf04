{"culture": "ar", "texts": {"AppName": "TaskTracking", "Menu:Home": "الصفحة الرئيسية", "Welcome": "مرحباً", "LongWelcomeMessage": "مرحبا بكم في التطبيق. هذا مشروع بدء تشغيل يعتمد على إطار عمل ABP. لمزيد من المعلومات ، يرجى زيارة abp.io.", "Permission:TaskTracking": "تتبع المهام", "Permission:TaskGroups": "مجموعات المهام", "Permission:TaskGroups.Create": "إنشاء مجموعات المهام", "Permission:TaskGroups.Update": "تحديث مجموعات المهام", "Permission:TaskGroups.Delete": "حذف مجموعات المهام", "Permission:TaskGroups.ManageUsers": "إدارة مستخدمي مجموعات المهام", "Permission:TaskGroups.RecordProgress": "تسجيل تقدم المهام", "Permission:TaskGroups.ManageTaskItems": "إدارة عناصر المهام", "Permission:TaskGroups.CreateTaskItems": "إنشاء عناصر المهام", "Permission:TaskGroups.UpdateTaskItems": "تحديث عناصر المهام", "Permission:TaskGroups.DeleteTaskItems": "حذف عناصر المهام", "Permission:TaskGroups.GenerateInvitations": "إنشاء دعوات", "Dashboard": "لوحة التحكم", "TaskManagementSystem": "نظام إدارة المهام", "WelcomeToDashboard": "مرحباً بك في لوحة المهام", "ManageTaskGroupsEfficiently": "إدارة مجموعات المهام بكفاءة وأناقة", "ActiveTaskGroups": "مجموعات المهام النشطة", "CreateNewGroup": "إنشاء مجموعة جديدة", "LoadingTaskGroups": "جاري تحميل مجموعات المهام...", "LoadMore": "تحميل المزيد", "NoTaskGroups": "لا توجد مجموعات مهام", "StartByCreatingTaskGroup": "ابدأ بإنشاء مجموعة مهام جديدة لتنظيم أعمالك", "CreateFirstTaskGroup": "إنشاء أول مجموعة مهام", "View": "<PERSON><PERSON><PERSON>", "Edit": "تعديل", "Options": "خيارات", "Completed": "مكتملة", "Expired": "منتهية الصلاحية", "Active": "نشطة", "OverallProgress": "التقدم العام", "MainMenu": "القائمة الرئيسية", "TaskGroups": "مجموعات المهام", "AllGroups": "جميع المجموعات", "MyGroups": "مجموعاتي", "CreateGroup": "إنشاء مجموعة", "Tasks": "المهام", "TodayTasks": "مها<PERSON> اليوم", "UpcomingTasks": "المهام القادمة", "CompletedTasks": "المهام المكتملة", "Settings": "الإعدادات", "Users": "المستخدمون", "Security": "الأمان", "About": "حو<PERSON> النظام", "Profile": "الملف الشخصي", "Logout": "تسجيل الخروج", "Notifications": "الإشعارات", "RTL": "RTL", "EditTaskGroup": "تعديل مجموعة المهام", "UpdateTaskGroupDetails": "تحديث تفاصيل ومعلومات مجموعة المهام", "LoadingTaskGroup": "جاري تحميل مجموعة المهام...", "TaskGroupNotFound": "مجموعة المهام غير موجودة أو ليس لديك صلاحية للوصول إليها", "Title": "العنوان", "Description": "الوصف", "StartDate": "تاريخ البداية", "EndDate": "تاريخ النهاية", "EnterTaskGroupTitle": "أدخل عنواناً وصفياً لمجموعة المهام", "EnterTaskGroupDescription": "أدخل وصفاً مفصلاً لمجموعة المهام", "OptionalEndDate": "تاريخ نهاية اختياري لمجموعة المهام", "Cancel": "إلغاء", "SaveChanges": "حفظ التغييرات", "TaskGroupUpdatedSuccessfully": "تم تحديث مجموعة المهام بنجاح!", "ErrorLoadingTaskGroup": "خطأ في تحميل مجموعة المهام. يرجى المحاولة مرة أخرى.", "ErrorUpdatingTaskGroup": "خطأ في تحديث مجموعة المهام. يرجى المحاولة مرة أخرى.", "EndDateMustBeAfterStartDate": "يجب أن يكون تاريخ النهاية بعد تاريخ البداية", "CreateTaskGroup": "إنشاء مجموعة مهام", "CreateNewTaskGroupDetails": "إنشاء مجموعة مهام جديدة لتنظيم أعمالك", "SelectStartDate": "اختر تاريخ البداية لمجموعة المهام", "CreateTaskGroupInfo": "سيتم تعيينك تلقائياً كمالك لهذه المجموعة. يمكنك إضافة أعضاء الفريق وتعيين المهام بعد الإنشاء.", "QuickStartTips": "نصائح البداية السريعة", "TipChooseDescriptiveTitle": "اختر عنواناً واضحاً ووصفياً يعكس الغرض من مجموعة المهام", "TipAddDetailedDescription": "أضف وصفاً مفصلاً لمساعدة أعضاء الفريق على فهم الأهداف والنطاق", "TipSetRealisticDates": "حدد تواريخ بداية ونهاية واقعية بناءً على الجدول الزمني لمشروعك", "TipEndDateOptional": "تاريخ النهاية اختياري - اتركه فارغاً للمجموعات المستمرة أو غير المحددة المدة", "TaskGroupCreatedSuccessfully": "تم إنشاء مجموعة المهام بنجاح!", "ErrorCreatingTaskGroup": "خطأ في إنشاء مجموعة المهام. يرجى المحاولة مرة أخرى.", "StartDateCannotBeInPast": "لا يمكن أن يكون تاريخ البداية في الماضي", "ViewAllTaskGroupsInSystem": "تصفح واستكشف جميع مجموعات المهام في النظام", "SearchTaskGroups": "البحث في مجموعات المهام...", "AllStatuses": "جميع الحالات", "SortBy": "ترتيب حسب", "CreationDate": "تاريخ الإنشاء", "Progress": "التقدم", "Filter": "تصفية", "ShowingResults": "عرض {0} من {1} نتيجة", "ErrorLoadingTaskGroups": "خطأ في تحميل مجموعات المهام. يرجى المحاولة مرة أخرى.", "NoTaskGroupsFound": "لم يتم العثور على مجموعات مهام", "NoTaskGroupsMatchingCriteria": "لا توجد مجموعات مهام تطابق معايير البحث. جرب تعديل المرشحات.", "ViewMyTaskGroups": "عرض وإدارة مجموعات المهام الخاصة بك", "TotalGroups": "إجمالي المجموعات", "ActiveGroups": "المجموعات النشطة", "CompletedGroups": "المجموعات المكتملة", "AverageProgress": "متوسط التقدم", "SearchMyGroups": "البحث في مجموعاتي...", "LoadingMyGroups": "جاري تحميل مجموعاتي...", "NoMyGroupsFound": "لم يتم العثور على مجموعات", "YouHaveNotCreatedAnyGroups": "لم تقم بإنشاء أي مجموعات مهام بعد. ابدأ بإنشاء مجموعتك الأولى!", "ErrorLoadingMyGroups": "خطأ في تحميل مجموعاتك. يرجى المحاولة مرة أخرى.", "TaskGroupDetails": "تفاصيل مجموعة المهام", "ViewTaskGroupDetailsAndManageTasks": "عرض تفاصيل مجموعة المهام وإدارة المهام", "TaskGroupNotFoundDescription": "مجموعة المهام التي تبحث عنها غير موجودة أو ليس لديك صلاحية للوصول إليها.", "BackToMyGroups": "العودة إلى مجموعاتي", "InProgress": "قيد التنفيذ", "NoDescriptionProvided": "لم يتم توفير وصف", "LoadingTasks": "جاري تحميل المهام...", "NoTasksFound": "لم يتم العثور على مهام", "NoTasksFoundDescription": "هذه المجموعة لا تحتوي على أي مهام بعد.", "CreateFirstTask": "إنشاء أول مهمة", "CreateTask": "إنشاء مهمة", "AddTask": "إضافة مهمة", "AnErrorOccurred": "<PERSON><PERSON><PERSON>", "OneTime": "مرة واحدة", "Recurring": "متكررة", "Overdue": "متأخرة", "DueToday": "مستحقة اليوم", "Pending": "معلقة", "Recurrence": "التكرار", "MarkComplete": "تحديد كمكتملة", "MarkIncomplete": "تحديد كغير مكتملة", "Daily": "يومياً", "Weekly": "أسبوعياً", "Monthly": "شهرياً", "Yearly": "سنوياً", "Custom": "مخصص", "TaskType": "نوع المهمة", "RecurrenceType": "نوع التكرار", "Interval": "الفترة", "DaysOfWeek": "أيام الأسبوع", "Occurrences": "<PERSON><PERSON><PERSON> التكرارات", "Date": "التاريخ", "TaskItemId": "معرف المهمة", "The {PropertyName} field is required.": "حقل {PropertyName} مطلوب.", "The {PropertyName} field must not exceed {MaxLength} characters.": "حقل {PropertyName} يج<PERSON> ألا يتجاوز {MaxLength} حرف.", "CreateNewTaskInGroup": "إنشاء مهمة جديدة في هذه المجموعة", "ViewGroup": "عرض المجموعة", "CreatingTaskForGroup": "إنشاء مهمة جديدة لهذه المجموعة", "EnterTaskTitle": "أدخل عنواناً وصفياً للمهمة", "EnterTaskDescription": "أدخل وصفاً مفصلاً للمهمة", "OneTimeTask": "مهمة لمرة واحدة", "OneTimeTaskDescription": "مهمة تحتاج إلى إنجاز مرة واحدة", "RecurringTask": "مهمة متكررة", "RecurringTaskDescription": "مهمة تتكرر وفقاً لجدول زمني", "RecurrencePattern": "نمط التكرار", "SelectRecurrenceType": "اختر كم مرة تتكرر هذه المهمة", "EveryNDays": "كل N أيام (مثل: كل يومين)", "EveryNWeeks": "كل N أسابيع (مثل: كل 3 أسابيع)", "EveryNMonths": "كل N أشهر (مثل: كل شهرين)", "EndCondition": "شرط الانتهاء", "EndByDate": "انتهاء بتاريخ محدد", "EndAfterOccurrences": "انتهاء بعد عدد من التكرارات", "RecurrenceEndDate": "تاريخ انتهاء التكرار", "SelectRecurrenceEndDate": "اختر متى يجب أن ينتهي التكرار", "NumberOfOccurrences": "<PERSON><PERSON><PERSON> التكرارات", "EnterNumberOfOccurrences": "أدخل كم مرة يجب أن تتكرر هذه المهمة", "CreateTaskInfo": "ستتم إضافة هذه المهمة إلى مجموعة المهام الحالية. سيتمكن جميع أعضاء المجموعة من رؤية هذه المهمة والعمل عليها.", "TaskCreationTips": "نصائح إنشاء المهام", "TipChooseDescriptiveTaskTitle": "اختر عنواناً واضحاً ومحدداً يصف ما يجب القيام به", "TipAddDetailedTaskDescription": "أضف تعليمات ومتطلبات مفصلة لمساعدة أعضاء الفريق على فهم المهمة", "TipSetRealisticTaskDates": "حدد تواريخ بداية ونهاية واقعية بناءً على تعقيد المهمة والتبعيات", "TipRecurringTasksHelp": "استخدم المهام المتكررة للأنشطة المنتظمة مثل المراجعات والتقارير والصيانة", "TaskCreatedSuccessfully": "تم إنشاء المهمة بنجاح!", "ErrorCreatingTask": "خطأ في إنشاء المهمة. يرجى المحاولة مرة أخرى.", "EditTask": "تعديل المهمة", "UpdateTaskDetails": "تحديث تفاصيل وإعدادات المهمة", "LoadingTask": "جاري تحميل المهمة...", "TaskNotFound": "المهمة غير موجودة", "TaskNotFoundDescription": "لا يمكن العثور على المهمة المطلوبة أو ليس لديك صلاحية للوصول إليها.", "BackToGroup": "العودة إلى المجموعة", "EditingTaskInGroup": "تعديل مهمة في هذه المجموعة", "EditTaskInfo": "التغييرات على هذه المهمة ستكون مرئية لجميع أعضاء المجموعة. كن حذراً عند تعديل المهام المتكررة لأنها قد تؤثر على التكرارات المستقبلية.", "TaskEditingTips": "نصائح تعديل المهام", "TipChangingTaskType": "تغيير نوع المهمة من متكررة إلى لمرة واحدة سيزيل نمط التكرار", "TipRecurrencePatternChanges": "تعديل أنماط التكرار يؤثر على مثيلات المهام المستقبلية، وليس المكتملة", "TipDateChangesAffectProgress": "تغيير التواريخ قد يؤثر على تتبع التقدم وحسابات تاريخ الاستحقاق", "TipSaveChangesRegularly": "احفظ التغييرات بانتظام لتجنب فقدان عملك", "TaskUpdatedSuccessfully": "تم تحديث المهمة بنجاح!", "ErrorUpdatingTask": "خطأ في تحديث المهمة. يرجى المحاولة مرة أخرى.", "TaskDetails": "تفاصيل المهمة", "ViewTaskDetailsAndInformation": "عرض تفاصيل ومعلومات المهمة", "NoEndDate": "لا يوجد تاريخ انتهاء", "NoEndCondition": "لا يوجد شرط انتهاء", "NoProgressRecorded": "لم يتم تسجيل أي تقدم بعد", "ProgressDescription": "تم إكمال {0} من {1} أيام", "ViewTask": "عرض المهمة", "ErrorLoadingTask": "خطأ في تحميل المهمة. يرجى المحاولة مرة أخرى.", "Sunday": "ال<PERSON><PERSON>د", "Monday": "الاثنين", "Tuesday": "الثلاثاء", "Wednesday": "الأربعاء", "Thursday": "الخميس", "Friday": "الجمعة", "Saturday": "السبت", "RecurrenceMustHaveEndDateOrOccurrences": "نمط التكرار يجب أن يحتوي على تاريخ انتهاء أو عدد تكرارات", "Recurrence must have an end date or a number of occurrences.": "يجب أن يكون للتكرار تاريخ نهاية أو عدد تكرارات.", "Recurrence cannot have both end date and number of occurrences.": "لا يمكن أن يكون للتكرار تاريخ نهاية وعدد تكرارات معاً.", "Progress date cannot be in the future.": "لا يمكن أن يكون تاريخ التقدم في المستقبل.", "TrackingTask:00001": "نطاق تاريخ غير صحيح. يجب أن يكون تاريخ النهاية بعد تاريخ البداية.", "TrackingTask:00002": "تاريخ نهاية المهمة يتجاوز تاريخ نهاية المجموعة.", "TrackingTask:00003": "لا يمكن إكمال المجموعة مع وجود مهام غير مكتملة.", "TrackingTask:00101": "لا يمكن تعيين نمط تكرار لمهمة لمرة واحدة.", "TrackingTask:00102": "نمط التكرار مطلوب للمهام المتكررة.", "TrackingTask:00103": "تم تسجيل التقدم بالفعل لهذا التاريخ.", "TrackingTask:00201": "فترة تكرار غير صحيحة. يجب أن تكون أكبر من 0.", "TrackingTask:00202": "عدد تكرارات غير صحيح. يجب أن يكون أكبر من 0.", "TrackingTask:00203": "التكرار الأسبوعي يتطلب تحديد أيام الأسبوع.", "TrackingTask:00204": "تاريخ نهاية التكرار يتجاوز تاريخ نهاية المهمة.", "TrackingTask:00301": "لا يمكن إزالة المالك من المجموعة.", "TrackingTask:00302": "المستخدم موجود بالفعل في المجموعة.", "TrackingTask:00401": "نسبة تقدم غير صحيحة. يجب أن تكون بين 0 و 100.", "TrackingTask:00402": "المستخدم ليس في المجموعة.", "TrackingTask:00403": "المهمة ليست في المجموعة.", "TrackingTask:00404": "التقدم موجود بالفعل لهذا المستخدم والمهمة.", "TrackingTask:00405": "التقدم غير موجود.", "TrackingTask:00406": "لا يمكن تغيير دور المالك.", "TrackingTask:00407": "لا يمكن التغيير إلى دور المالك.", "TrackingTask:00501": "الدعوة غير موجودة.", "TrackingTask:00502": "انتهت صلاحية الدعوة.", "TrackingTask:00503": "تم استخدام الدعوة بالفعل.", "TrackingTask:00504": "وصلت الدعوة إلى الحد الأقصى لعدد الاستخدامات.", "TrackingTask:00505": "رمز دعوة غير صحيح.", "TrackingTask:00506": "لا يمكن إنشاء دعوة لمجموعة غير نشطة.", "TrackingTask:00205": "يجب أن تحتوي التكرارات على تاريخ انتهاء أو عدد من التكرارات.", "TaskRecurrence_EveryNDays": "كل {0} يوم", "TaskRecurrence_EveryNWeeks": "كل {0} أسبوع", "TaskRecurrence_EveryNMonths": "كل {0} شهر", "TaskRecurrence_NoRecurrence": "لا تكرار", "RecordProgress": "تسجيل التقدم", "CurrentProgress": "التقدم الحالي", "QuickActions": "إجراءات سريعة", "MarkCompletedToday": "تحديد كمكتمل اليوم", "ProgressCalendar": "تقويم التقدم", "SelectCompletionDate": "اختر تاريخ الإكمال", "CompletionDate": "تاريخ الإكمال", "TaskFullyCompleted": "تم إكمال هذه المهمة بالكامل!", "ProgressRecordedSuccessfully": "تم تسجيل التقدم بنجاح!", "ErrorRecordingProgress": "حدث خطأ أثناء تسجيل التقدم. الرجاء المحاولة مرة أخرى.", "TaskCompleted": "اكتملت المهمة", "ProgressRemaining": "المتبقي {0}", "Due": "مستحق", "ErrorLoadingProgressDetail": "حدث خطأ أثناء تحميل تفاصيل التقدم. الرجاء المحاولة مرة أخرى.", "Close": "إغلاق", "TaskTracking:00104": "تاريخ التقدم لا يمكن أن يكون في المستقبل.", "Delete": "<PERSON><PERSON><PERSON>", "DeleteTask": "<PERSON>ذ<PERSON> المهمة", "DeleteTaskConfirmation": "هل أنت متأكد من أنك تريد حذف المهمة '{0}'؟ لا يمكن التراجع عن هذا الإجراء.", "TaskDeletedSuccessfully": "تم حذف المهمة بنجاح!", "ProgressRemovedSuccessfully": "تمت إزالة التقدم بنجاح!", "ErrorRemovingProgress": "حد<PERSON> خطأ أثناء إزالة التقدم. الرجاء المحاولة مرة أخرى.", "ViewTasksDueToday": "عرض جميع المهام المستحقة اليوم", "TasksDueToday": "المهام المستحقة اليوم", "SearchTasks": "البحث في المهام...", "AllTasks": "جميع المهام", "AllTypes": "جميع الأنواع", "Refresh": "تحديث", "TasksRefreshed": "تم تحديث المهام بنجاح!", "LoadingMoreTasks": "جاري تحميل المزيد من المهام...", "NoTasksDueToday": "لا توجد مهام مستحقة اليوم", "NoTasksDueTodayDescription": "ليس لديك أي مهام مستحقة اليوم. عمل رائع في مواكبة عملك!", "NoCompletedTasks": "لا توجد مهام مكتملة", "NoCompletedTasksDescription": "لم يتم العثور على مهام مكتملة مع المرشحات الحالية.", "NoPendingTasks": "لا توجد مهام معلقة", "NoPendingTasksDescription": "لم يتم العثور على مهام معلقة مع المرشحات الحالية.", "NoOverdueTasks": "لا توجد مهام متأخرة", "NoOverdueTasksDescription": "لم يتم العثور على مهام متأخرة مع المرشحات الحالية.", "NoTasksMatchSearch": "لا توجد مهام تطابق معايير البحث الخاصة بك. جرب تعديل مصطلحات البحث أو المرشحات.", "ClearFilters": "م<PERSON><PERSON> المرشحات", "ErrorLoadingTasks": "خطأ في تحميل المهام. يرجى المحاولة مرة أخرى.", "Status": "الحالة", "ViewUpcomingTasks": "عرض جميع المهام القادمة في الأيام القليلة القادمة", "TasksUpcoming": "المهام القادمة", "DaysAhead": "الأيام القادمة", "Next7Days": "الأيام السبعة القادمة", "Next14Days": "الأيام الأربعة عشر القادمة", "Next30Days": "الأيام الثلاثين القادمة", "CustomDays": "أيام مخصصة", "NoUpcomingTasks": "لا توجد مهام قادمة", "NoUpcomingTasksDescription": "ليس لديك أي مهام قادمة في الفترة الزمنية المحددة. عمل رائع في البقاء منظماً!", "ErrorLoadingUpcomingTasks": "خطأ في تحميل المهام القادمة. يرجى المحاولة مرة أخرى.", "UpcomingTasksRefreshed": "تم تحديث المهام القادمة بنجاح!", "TasksDue": "المهام المستحقة", "InNext": "في الـ", "Days": "أيام", "ExpirationHours": "ساعات انتهاء الصلاحية", "MaxUses": "الح<PERSON> الأقصى للاستخدامات", "DefaultRole": "الدور الافتراضي", "InvitationToken": "رمز الدعوة", "Cannot set default role to Owner through invitations.": "لا يمكن تعيين الدور الافتراضي كمالك من خلال الدعوات.", "The {PropertyName} field must be exactly {ExpectedLength} characters long.": "يجب أن يكون حقل {PropertyName} بطول {ExpectedLength} حرف بالضبط.", "The {PropertyName} field contains invalid characters.": "يحتوي حقل {PropertyName} على أحرف غير صحيحة.", "The {PropertyName} field must be greater than 0.": "يجب أن يكون حقل {PropertyName} أكبر من 0.", "The {PropertyName} field must not exceed {ComparisonValue} hours.": "يجب ألا يتجاوز حقل {PropertyName} {ComparisonValue} ساعة.", "The {PropertyName} field must be 0 or greater.": "يجب أن يكون حقل {PropertyName} 0 أو أكبر.", "The {PropertyName} field must not exceed {ComparisonValue}.": "يجب ألا يتجاوز حقل {PropertyName} {ComparisonValue}.", "The {PropertyName} field has an invalid value.": "حقل {PropertyName} يحتوي على قيمة غير صحيحة.", "Days of week should only be specified for weekly recurrence.": "يجب تحديد أيام الأسبوع فقط للتكرار الأسبوعي.", "ManageUsers": "إدارة المستخدمين", "ManageUsersDescription": "إضافة وإزالة وإدارة أدوار المستخدمين في مجموعة المهام هذه", "CurrentUsers": "المستخدمون الحاليون", "AddUsers": "إضافة مستخدمين", "NoUsersInGroup": "لا يوجد مستخدمون في المجموعة", "NoUsersInGroupDescription": "مجموعة المهام هذه لا تحتوي على أي مستخدمين بعد.", "SearchUsers": "البحث عن المستخدمين", "SearchUsersPlaceholder": "البحث بالاسم المستخدم أو البريد الإلكتروني أو الاسم...", "SearchForUsers": "البحث عن المستخدمين", "SearchForUsersDescription": "أدخل مصطلح بحث للعثور على المستخدمين لإضافتهم إلى هذه المجموعة.", "NoUsersFound": "لم يتم العثور على مستخدمين", "NoUsersFoundDescription": "لا يوجد مستخدمون يطابقون معايير البحث الخاصة بك. جرب مصطلحات بحث مختلفة.", "AlreadyInGroup": "موجود بالفعل في المجموعة", "Add": "إضافة", "Remove": "إزالة", "RemoveUser": "إزالة المستخدم", "RemoveUserConfirmation": "هل أنت متأكد من أنك تريد إزالة '{0}' من مجموعة المهام هذه؟", "ChangeRole": "تغيير الدور", "SelectRole": "اختر الدور", "SelectRoleForUser": "اختر دوراً لـ {0}:", "ChangeRoleForUser": "تغيير دور {0}:", "Role:Owner": "مالك", "Role:CoOwner": "مالك مشارك", "Role:Subscriber": "مشترك", "Owner": "مالك", "SubscriberRoleDescription": "يمكنه عرض وإكمال المهام في المجموعة", "CoOwnerRoleDescription": "يمكنه إدارة المهام وإضافة/إزالة المشتركين", "RoleSelectionNote": "ملاحظة: يمكن للمالكين فقط تعيين دور المالك للمستخدمين الآخرين.", "AddUser": "إضافة مستخدم", "UserAddedSuccessfully": "تمت إضافة المستخدم '{0}' إلى المجموعة بنجاح!", "UserRemovedSuccessfully": "تمت إزالة المستخدم '{0}' من المجموعة بنجاح!", "UserRoleChangedSuccessfully": "تم تغيير دور المستخدم '{0}' بنجاح!", "ErrorLoadingUsers": "خطأ في تحميل المستخدمين. يرجى المحاولة مرة أخرى.", "ErrorSearchingUsers": "خطأ في البحث عن المستخدمين. يرجى المحاولة مرة أخرى.", "ErrorAddingUser": "خطأ في إضافة المستخدم. يرجى المحاولة مرة أخرى.", "ErrorRemovingUser": "خطأ في إزالة المستخدم. يرجى المحاولة مرة أخرى.", "ErrorChangingUserRole": "خطأ في تغيير دور المستخدم. يرجى المحاولة مرة أخرى.", "BackToTaskGroup": "العودة إلى مجموعة المهام", "TaskGroup": "مجموعة المهام", "AddFirstUser": "إضافة أول مستخدم", "UsersInGroup": "{0} مستخدم في المجموعة", "SearchResultsCount": "{0} نتيجة بحث", "Searching": "جاري البحث...", "Search": "ب<PERSON><PERSON>", "OwnerRoleDescription": "يملك السيطرة الكاملة على مجموعة المهام ويمكنه إدارة جميع الجوانب بما في ذلك المستخدمين والإعدادات", "Invitations": "الدعوات", "CreateInvitation": "إنشاء دعوة", "CreateInvitationLink": "إنشاء رابط دعوة", "InvitationCreateDescription": "قم بإنشاء رابط دعوة لدعوة المستخدمين للانضمام إلى مجموعة المهام هذه. يمكنك تخصيص إعدادات انتهاء الصلاحية والاستخدام والدور الافتراضي.", "ExpirationHoursHelp": "عدد الساعات حتى انتهاء صلاحية الدعوة (1-720 ساعة)", "MaxUsesHelp": "الحد الأقصى لعدد المرات التي يمكن استخدام هذه الدعوة فيها (0 = غير محدود)", "InvitationPreview": "معاينة الدعوة", "ExpiresOn": "تنتهي في", "MaxUsage": "الح<PERSON> الأقصى للاستخدام", "JoinersWillHaveRole": "سيحصل المنضمون على دور", "Creating": "جاري الإنشاء", "CreateFirstInvitation": "إنشاء أول دعوة", "InvitationsCount": "الدعوات ({0})", "NoInvitations": "لا توجد دعوات", "NoInvitationsDescription": "لم يتم إنشاء أي دعوات لمجموعة المهام هذه بعد. قم بإنشاء دعوة لدعوة المستخدمين للانضمام.", "CopyLink": "نسخ الرابط", "Usage": "الاستخدام", "UsageUnlimited": "تم الاستخدام {0} مرة (غير محدود)", "UsageLimited": "تم الاستخدام {0} من {1} مرة", "ExpirationDate": "تاريخ انتهاء الصلاحية", "CreatedBy": "أنشأ بواسطة", "CopyInvitationLink": "نسخ رابط الدعوة", "InvitationLinkCopied": "تم نسخ رابط الدعوة إلى الحافظة!", "ErrorCopyingLink": "خطأ في نسخ الرابط. يرجى المحاولة مرة أخرى.", "DeleteInvitation": "<PERSON>ذ<PERSON> الدعوة", "DeleteInvitationConfirmation": "هل أنت متأكد من أنك تريد حذف هذه الدعوة؟ لن يتمكن المستخدمون من استخدام هذا الرابط بعد الآن.", "InvitationDeletedSuccessfully": "تم حذف الدعوة بنجاح!", "ErrorDeletingInvitation": "خطأ في حذف الدعوة. يرجى المحاولة مرة أخرى.", "InvitationCreatedSuccessfully": "تم إنشاء الدعوة بنجاح!", "ErrorCreatingInvitation": "خطأ في إنشاء الدعوة. يرجى المحاولة مرة أخرى.", "ErrorLoadingInvitations": "خطأ في تحميل الدعوات. يرجى المحاولة مرة أخرى.", "MaxUsesReached": "تم الوصول للحد الأقصى", "Invalid": "غير صالحة", "JoinTaskGroup": "الانضمام إلى مجموعة المهام", "LoadingInvitation": "جاري تحميل الدعوة...", "InvitationError": "خطأ في الدعوة", "YouHaveBeenInvitedToJoin": "تمت دعوتك للانضمام إلى مجموعة المهام هذه", "InvitedBy": "دعوة من", "YourRole": "دورك", "InvitationDetails": "تفاصيل الدعوة", "InvitationExpiredMessage": "انتهت صلاحية هذه الدعوة ولا يمكن استخدامها.", "InvitationMaxUsesReachedMessage": "وصلت هذه الدعوة إلى الحد الأقصى لعدد الاستخدامات.", "InvitationInvalidMessage": "هذه الدعوة غير صالحة أو لا يمكن استخدامها.", "JoinGroup": "الانضمام إلى المجموعة", "Joining": "جاري الانضمام", "PleaseLoginToJoin": "يرجى تسجيل الدخول للانضمام إلى مجموعة المهام", "Login": "تسجيل الدخول", "GoToDashboard": "الذهاب إلى لوحة التحكم", "InvalidInvitationToken": "رمز دعوة غير صحيح", "InvitationNotFound": "لم يتم العثور على الدعوة", "ErrorLoadingInvitation": "خطأ في تحميل الدعوة. يرجى المحاولة مرة أخرى.", "SuccessfullyJoinedGroup": "تم الانضمام بنجاح إلى '{0}'!", "AlreadyMemberOfGroup": "أنت عضو بالفعل في هذه المجموعة", "ErrorJoiningGroup": "خطأ في الانضمام إلى المجموعة. يرجى المحاولة مرة أخرى.", "Unlimited": "<PERSON>ير محدود"}}